# 额外服务价格计算修复测试

## 问题描述
- 后台配置：交付时间额外服务 +3元，修改次数额外服务 +5元
- 前端显示：交付时间额外服务 +13元，修改次数额外服务 +15元
- 问题：额外服务价格多了10元（基础服务包价格）

## 修复方案
修改了 `pc-src/pages/goods_detail/_id/index.vue` 中的 `extraServices()` computed 方法：

### 修复逻辑
1. 从productValue中找到包含该服务的最简单组合
2. 计算该组合的总价格减去基础服务包价格
3. 如果有多个额外服务，优先寻找只包含单个服务的组合
4. 如果找不到单个服务组合，使用平均分配

### 关键代码变更
```javascript
// 从productValue中找到包含该服务的最简单组合来计算价格
let servicePrice = 0;
let minExtraServicesCount = Infinity;
let bestMatch = null;

// 遍历所有productValue，找到包含该服务且额外服务最少的组合
for (const key in this.productValue) {
  const valueData = this.productValue[key];
  
  if (!valueData.sku || !valueData.sku.includes(serviceName)) {
    continue;
  }
  
  const skuParts = valueData.sku.split(',').map(part => part.trim());
  
  // 计算额外服务数量（排除基础包和"不选择"）
  const extraServicesCount = skuParts.filter(part => 
    part !== '不选择' && 
    !['Basic', 'Standard', 'Premium'].includes(part)
  ).length;
  
  // 找到额外服务最少的组合
  if (extraServicesCount < minExtraServicesCount) {
    minExtraServicesCount = extraServicesCount;
    bestMatch = {
      key: key,
      valueData: valueData,
      skuParts: skuParts,
      extraServicesCount: extraServicesCount
    };
  }
}
```

## 测试步骤
1. 启动前端项目：`npm run dev`
2. 访问服务包商品详情页
3. 检查额外服务价格显示是否正确
4. 验证总价计算是否准确

## 预期结果
- 交付时间额外服务显示：+3元
- 修改次数额外服务显示：+5元
- 总价计算：基础服务包价格 + 选中的额外服务价格

## 调试信息
修复后的代码会在浏览器控制台输出详细的调试信息：
- 处理的服务选项
- 找到的最佳匹配组合
- 基础服务包价格
- 计算出的额外服务价格

## 备注
如果问题仍然存在，可能需要检查：
1. 后台生成的productValue数据结构
2. 基础服务包价格的获取逻辑
3. 规格组合的生成逻辑
